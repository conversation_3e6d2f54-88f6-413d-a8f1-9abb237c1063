<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Uscreen Integration - J&J Productions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .product-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
        }
        .product-description {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .price-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .price {
            font-size: 2em;
            font-weight: bold;
            color: #0066cc;
        }
        .purchase-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .purchase-button:hover {
            background: #218838;
        }
        .sl24-gopay-button {
            background: #0066cc !important;
            color: white !important;
            border: none !important;
            padding: 15px 30px !important;
            font-size: 1.2em !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            margin-right: 15px !important;
            margin-bottom: 10px !important;
        }
        .sl24-gopay-button:hover {
            background: #0052a3 !important;
        }
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 20px;
            margin-top: 30px;
        }
        .test-info h3 {
            color: #0066cc;
            margin-top: 0;
        }
        .user-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="product-container">
        <h1 class="product-title">J&J Productions</h1>
        
        <div class="product-description">
            Exkluzivní sportovní událost od J&J Productions. Sledujte nejlepší zápasy a získejte přístup k prémiového obsahu.
        </div>
        
        <div class="user-info">
            <strong>Testovací uživatel:</strong> <EMAIL> (ID: 24141326)
        </div>
        
        <div class="price-section">
            <div class="price" data-price="999">999 CZK</div>
            <div>Jednorázová platba za plný přístup</div>
        </div>
        
        <!-- Původní Uscreen tlačítko (bude nahrazeno) -->
        <button class="purchase-button" data-uscreen-purchase data-product-id="3647641">
            Koupit přes Uscreen
        </button>
        
        <!-- Naše GoPay tlačítko -->
        <button class="sl24-gopay-button" data-product-id="3647641">
            Koupit přes GoPay
        </button>
        
        <div class="test-info">
            <h3>🧪 Test Informace</h3>
            <p><strong>Product ID:</strong> 3647641</p>
            <p><strong>Product Slug:</strong> jj-productions</p>
            <p><strong>Price:</strong> 999 CZK</p>
            <p><strong>Test User:</strong> <EMAIL></p>
            
            <h4>Očekávané chování:</h4>
            <ul>
                <li>Zelené tlačítko "Koupit přes Uscreen" by mělo být nahrazeno modrým GoPay tlačítkem</li>
                <li>Kliknutí na GoPay tlačítko vytvoří platbu a přesměruje na GoPay</li>
                <li>Po úspěšné platbě se uživateli udělí přístup k produktu</li>
                <li>Systém automaticky detekuje přihlášeného uživatele nebo zobrazí formulář</li>
            </ul>
        </div>
        
        <div class="debug-panel" id="debug-panel">
            <strong>Debug Log:</strong><br>
            <div id="debug-log">Načítání...</div>
        </div>
    </div>

    <!-- SportLive24 Payment Integration Script -->
    <script>
        // Simulace Uscreen uživatele pro testování
        window.Uscreen = {
            user: {
                id: 24141326,
                email: '<EMAIL>',
                name: 'Vladislav Vaněček',
                first_name: 'Vladislav',
                last_name: 'Vaněček'
            }
        };
        
        // Debug logging
        const debugLog = document.getElementById('debug-log');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        function addToDebugLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            debugLog.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToDebugLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToDebugLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleError.apply(console, args);
            addToDebugLog(args.join(' '), 'warn');
        };
        
        // Clear debug log
        debugLog.innerHTML = '';
        
        console.log('🧪 Test page loaded');
        console.log('👤 Simulated user:', window.Uscreen.user);
    </script>
    
    <!-- Load our payment integration script -->
    <script src="/static/js/sportlive24-payment-integration.js"></script>
    
    <script>
        // Additional test functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM loaded, integration should be active');
            
            // Log current URL for slug extraction testing
            console.log('🔗 Current URL:', window.location.href);
            console.log('📍 Current path:', window.location.pathname);
        });
    </script>
</body>
</html>
