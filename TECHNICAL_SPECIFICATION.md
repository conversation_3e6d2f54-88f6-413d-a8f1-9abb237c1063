# 🔧 TECHNICAL SPECIFICATION - GOPAY INTEGRATION

## 📋 SYSTEM OVERVIEW

**Purpose:** Replace Uscreen checkout with GoPay payment system for Czech customers
**Architecture:** JavaScript frontend + Django REST API backend + GoPay payment gateway

---

## 🚀 API ENDPOINTS

### **Base URL:** `https://pays.sportlive24.tv/api/`

| Endpoint | Method | Purpose | Required Fields |
|----------|--------|---------|----------------|
| `/create-payment/` | POST | Create new payment | email, event_id, price |
| `/product/{slug}/` | GET | Get product info | - |
| `/payment-status/{id}/` | GET | Check payment status | - |
| `/notify/` | POST | GoPay webhook | (GoPay data) |

### **Payment Creation Request:**
```json
POST /api/create-payment/
{
    "email": "<EMAIL>",
    "event_id": "jj-productions",
    "price": 999.00,
    "user_name": "<PERSON>"
}
```

### **Payment Creation Response:**
```json
{
    "payment_url": "https://gw.gopay.com/gw/v3/abc123",
    "payment_id": "3280338451",
    "status": "created"
}
```

---

## 📱 FRONTEND INTEGRATION

### **JavaScript File:**
```
https://pays.sportlive24.tv/static/js/sportlive24-payment-integration.js
```

### **Key Features:**
- **Auto-detects** logged-in Uscreen users
- **Replaces** existing purchase buttons
- **Handles** anonymous users with email form
- **Redirects** to GoPay payment gateway
- **Responsive** design with error handling

### **User Detection Priority:**
1. `window.Uscreen.user` (Uscreen session)
2. `localStorage.getItem('user_email')`
3. Browser cookies
4. Manual email input form

---

## 🎯 PRODUCT CONFIGURATION

### **Current Test Product:**
```javascript
{
    "product_id": 3647641,
    "slug": "jj-productions", 
    "name": "J&J Productions",
    "price": 999.00,
    "currency": "CZK"
}
```

### **Test User:**
```javascript
{
    "uscreen_id": 24141326,
    "email": "<EMAIL>",
    "name": "Vladislav Vaněček"
}
```

---

## 🔄 PAYMENT WORKFLOW

```
1. User clicks GoPay button
2. JavaScript detects user or shows form
3. API creates payment in GoPay
4. User redirected to GoPay gateway
5. User completes payment
6. GoPay sends webhook notification
7. Backend processes payment
8. User access granted in Uscreen
9. Confirmation email sent
```

---

## 🔐 SECURITY & AUTHENTICATION

### **API Security:**
- HTTPS only (SSL/TLS)
- CSRF protection
- Rate limiting
- Input validation

### **GoPay Integration:**
- Production credentials configured
- Webhook signature verification
- Secure payment URLs
- PCI DSS compliance

### **Uscreen Integration:**
- API key authentication
- User verification
- Access control
- Data synchronization

---

## 🧪 TESTING

### **Test Environment:**
- **Test Page:** https://pays.sportlive24.tv/test/uscreen-integration/
- **API Docs:** https://pays.sportlive24.tv/swagger/
- **Admin Panel:** https://pays.sportlive24.tv/admin/

### **Test Scenarios:**
1. Logged-in user payment
2. Anonymous user payment
3. Button replacement
4. Error handling
5. Webhook processing

---

## 📊 MONITORING

### **Logging:**
- All payments logged
- API requests tracked
- Errors with stack traces
- Webhook notifications

### **Metrics:**
- Payment success rate
- API response times
- User conversion rate
- Error frequency

---

## 🚨 ERROR HANDLING

### **Frontend Errors:**
- Network connectivity issues
- Invalid user data
- Payment creation failures
- Redirect problems

### **Backend Errors:**
- GoPay API failures
- Uscreen API issues
- Database problems
- Webhook processing errors

### **User Experience:**
- Clear error messages
- Fallback options
- Retry mechanisms
- Support contact info

---

## 🔧 IMPLEMENTATION FOR USCREEN DEVELOPER

### **Step 1: Add Head Code**
```html
<script src="https://pays.sportlive24.tv/static/js/sportlive24-payment-integration.js"></script>
<style>
.sl24-gopay-button {
    background: #0066cc !important;
    color: white !important;
    padding: 15px 30px !important;
    border-radius: 6px !important;
    font-size: 1.2em !important;
}
</style>
```

### **Step 2: Add Product Button**
```html
<button class="sl24-gopay-button" 
        data-product-id="3647641"
        data-product-slug="jj-productions"
        data-price="999">
    Buy for 999 CZK via GoPay
</button>
```

### **Step 3: Test Integration**
1. Visit product page
2. Click GoPay button
3. Verify redirect to GoPay
4. Complete test payment
5. Check access granted

---

## 📞 SUPPORT

### **Technical Contact:**
- **Email:** <EMAIL>
- **Documentation:** Complete API docs available
- **Test Environment:** Full testing setup provided

### **Implementation Support:**
- Real-time debugging available
- API testing tools provided
- Integration assistance offered

---

## ✅ REQUIREMENTS CHECKLIST

**For Uscreen Developer:**
- [ ] Access to Uscreen admin panel
- [ ] Ability to modify Head Code
- [ ] Ability to edit product pages
- [ ] Basic JavaScript knowledge
- [ ] Testing environment access

**System Requirements:**
- [ ] HTTPS enabled
- [ ] Modern browser support
- [ ] JavaScript enabled
- [ ] CORS configured
- [ ] Webhook endpoint accessible

**Estimated Implementation Time: 2-3 hours**
