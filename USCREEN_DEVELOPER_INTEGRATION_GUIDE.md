# 🔧 USCREEN DEVELOPER INTEGRATION GUIDE

## 📋 OVERVIEW

This document explains how to integrate our **GoPay payment system** with Uscreen platform. Our backend automatically handles payment processing and grants access to purchased content.

### **System Architecture:**
```
Uscreen Product Page → JavaScript Integration → GoPay Payment → Webhook → Access Grant
```

---

## 🚀 BACKEND API ENDPOINTS

### **Base URL:** `https://dev.sportlive24.tv/api/`

### **1. Create Payment**
```http
POST /api/create-payment/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "event_id": "product-slug",
    "price": 999.00,
    "user_name": "<PERSON>",
    "check_user": true
}
```

**Response:**
```json
{
    "payment_url": "https://gw.gopay.com/gw/v3/...",
    "payment_id": "3280338451",
    "status": "created"
}
```

### **2. Get Product Info**
```http
GET /api/product/{product_slug}/
```

**Response:**
```json
{
    "product_id": 3647641,
    "slug": "jj-productions",
    "name": "J&J Productions",
    "price": 999.00,
    "currency": "CZK",
    "available": true
}
```

### **3. Payment Status**
```http
GET /api/payment-status/{payment_id}/
```

**Response:**
```json
{
    "payment_id": "3280338451",
    "status": "PAID",
    "email": "<EMAIL>",
    "event_id": "jj-productions",
    "price": 999.0
}
```

---

## 📱 JAVASCRIPT INTEGRATION

### **1. Include Script in Uscreen Head Code:**

```html
<!-- Add to Uscreen Settings → Custom Code → Head Code -->
<script>
// Configuration
window.SportLive24Config = {
    API_BASE_URL: 'https://dev.sportlive24.tv/api',
    DEBUG: false,
    ENVIRONMENT: 'production'
};
</script>

<!-- Load integration script -->
<script src="https://dev.sportlive24.tv/static/js/sportlive24-payment-integration.js"></script>

<!-- Styling for GoPay buttons -->
<style>
.sl24-gopay-button {
    background: #0066cc !important;
    color: white !important;
    border: none !important;
    padding: 15px 30px !important;
    font-size: 1.2em !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: background-color 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    font-weight: bold !important;
}

.sl24-gopay-button:hover {
    background: #0052a3 !important;
    color: white !important;
}

/* Hide original Uscreen buttons when replaced */
.uscreen-purchase-replaced {
    display: none !important;
}
</style>
```

### **2. Product Page Integration:**

#### **Option A: Automatic Button Replacement**
The JavaScript automatically replaces existing Uscreen purchase buttons. Just ensure the page has:

```html
<!-- Set product data attributes on body or container -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.body.setAttribute('data-product-id', '3647641');
    document.body.setAttribute('data-product-slug', 'jj-productions');
    document.body.setAttribute('data-product-price', '999');
});
</script>
```

#### **Option B: Manual Button Placement**
```html
<!-- Custom GoPay button -->
<button class="sl24-gopay-button"
        data-product-id="3647641"
        data-product-slug="jj-productions"
        data-price="999">
    Buy for 999 CZK via GoPay
</button>

<!-- Fallback for JavaScript disabled -->
<noscript>
    <a href="https://pays.sportlive24.tv/create-payment-from-product/?product_slug=jj-productions&price=999&email="
       class="sl24-gopay-button">
        Buy for 999 CZK
    </a>
</noscript>
```

---

## 🔄 PAYMENT WORKFLOW

### **1. User Flow:**
1. User visits product page on sportlive24.tv
2. JavaScript detects logged-in user OR shows email form
3. User clicks GoPay button
4. System creates payment and redirects to GoPay
5. User completes payment
6. GoPay sends webhook to our backend
7. Backend grants access to user in Uscreen
8. User receives confirmation email

### **2. Technical Flow:**
```javascript
// 1. User detection
const user = window.Uscreen?.user || getUserFromStorage();

// 2. Payment creation
const response = await fetch('/api/create-payment/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: user.email,
        event_id: 'jj-productions',
        price: 999.00,
        user_name: user.name
    })
});

// 3. Redirect to payment
window.location.href = response.payment_url;
```

---

## 🎯 PRODUCT CONFIGURATION

### **Current Test Product:**
- **Product ID:** `3647641`
- **Slug:** `jj-productions`
- **Name:** `J&J Productions`
- **Price:** `999 CZK`
- **Test User:** `<EMAIL>` (ID: 24141326)

### **Adding New Products:**
1. Product must exist in Uscreen
2. Add product data to our backend via API or admin
3. Configure button with correct `data-product-id` and `data-product-slug`

---

## 🔐 USER AUTHENTICATION

### **Automatic User Detection:**
The JavaScript automatically detects users from:

1. **Uscreen Session:** `window.Uscreen.user`
2. **Local Storage:** `localStorage.getItem('user_email')`
3. **Cookies:** Various user-related cookies
4. **Manual Input:** Email form if no user detected

### **User Data Structure:**
```javascript
const user = {
    id: 24141326,
    email: '<EMAIL>',
    name: 'John Doe',
    authenticated: true
};
```

---

## 🔔 WEBHOOK NOTIFICATIONS

### **GoPay Webhook Endpoint:**
```
POST https://pays.sportlive24.tv/api/notify/
```

### **Webhook Processing:**
1. Receives payment notification from GoPay
2. Validates payment status
3. Checks if user exists in Uscreen
4. Creates user if doesn't exist
5. Grants access to purchased product
6. Sends confirmation email

---

## 🧪 TESTING

### **Test Environment:**
- **Test Page:** `https://pays.sportlive24.tv/test/uscreen-integration/`
- **API Docs:** `https://pays.sportlive24.tv/swagger/`
- **Admin Panel:** `https://pays.sportlive24.tv/admin/`

### **Test Scenarios:**
1. **Logged-in User:** Should auto-detect and create payment
2. **Anonymous User:** Should show email form
3. **Button Replacement:** Original Uscreen buttons should be replaced
4. **Payment Flow:** Should redirect to GoPay and process webhook

### **Debug Mode:**
```javascript
// Enable debug logging
window.SportLive24Config.DEBUG = true;
```

---

## 📊 MONITORING & LOGS

### **Client-Side Debugging:**
```javascript
// Check if script loaded
console.log('SportLive24 Integration:', window.SportLive24PaymentIntegration);

// Check user detection
console.log('Detected user:', window.Uscreen?.user);

// Check product data
console.log('Product data:', document.body.dataset);
```

### **Server-Side Monitoring:**
- **Payment Logs:** Available in Django admin
- **API Status:** `GET /api/` returns system status
- **Error Tracking:** All errors logged with timestamps

---

## 🚨 TROUBLESHOOTING

### **Common Issues:**

1. **Script Not Loading:**
   - Check CORS headers
   - Verify script URL accessibility
   - Check browser console for errors

2. **Buttons Not Replacing:**
   - Ensure selectors match Uscreen's button classes
   - Check if DOM is fully loaded
   - Verify product data attributes

3. **Payment Creation Fails:**
   - Check API endpoint accessibility
   - Verify request format
   - Check user email validation

4. **User Not Detected:**
   - Check Uscreen session object
   - Verify localStorage/cookies
   - Implement fallback email form

### **Debug Commands:**
```javascript
// Test API connectivity
fetch('https://pays.sportlive24.tv/api/').then(r => r.json()).then(console.log);

// Test product info
fetch('https://pays.sportlive24.tv/api/product/jj-productions/').then(r => r.json()).then(console.log);

// Check current user
console.log('Uscreen user:', window.Uscreen?.user);
```

---

## 📞 SUPPORT

### **Technical Contact:**
- **Email:** <EMAIL>
- **API Documentation:** https://pays.sportlive24.tv/swagger/
- **Test Environment:** https://pays.sportlive24.tv/test/uscreen-integration/

### **Implementation Support:**
- All endpoints are documented with OpenAPI/Swagger
- Test environment available for integration testing
- Real-time logs available for debugging

---

## ✅ IMPLEMENTATION CHECKLIST

**For Uscreen Developer:**

- [ ] Add Head Code script to Uscreen
- [ ] Add CSS styling for GoPay buttons
- [ ] Configure product pages with data attributes
- [ ] Test button replacement functionality
- [ ] Test payment creation flow
- [ ] Verify user detection works
- [ ] Test with both logged-in and anonymous users
- [ ] Confirm webhook processing works
- [ ] Validate access granting in Uscreen

**Estimated Implementation Time: 2-3 hours**

---

## 📋 QUICK START FOR USCREEN DEVELOPER

### **Minimal Implementation (15 minutes):**

1. **Add to Uscreen Head Code:**
```html
<script src="https://pays.sportlive24.tv/static/js/sportlive24-payment-integration.js"></script>
```

2. **Add to J&J Productions page:**
```html
<button class="sl24-gopay-button" data-product-id="3647641">Buy via GoPay (999 CZK)</button>
```

3. **Test:** Visit page, click button, verify redirect to GoPay

### **Full Implementation Includes:**
- User auto-detection from Uscreen session
- Automatic button replacement
- Email form fallback for anonymous users
- Webhook processing for access granting
- Error handling and logging

---

## 🔗 IMPORTANT URLS

- **Production API:** https://dev.sportlive24.tv/api/
- **JavaScript File:** https://dev.sportlive24.tv/static/js/sportlive24-payment-integration.js
- **Test Page:** https://dev.sportlive24.tv/test/uscreen-integration/
- **API Documentation:** https://dev.sportlive24.tv/swagger/
- **Admin Panel:** https://dev.sportlive24.tv/admin/
