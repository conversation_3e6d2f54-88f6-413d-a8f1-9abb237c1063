/**
 * SportLive24 Payment Integration
 * JavaScript pro automatické přesměrování na platbu z Uscreen stránek produktů
 *
 * Použití:
 * 1. <PERSON><PERSON><PERSON>te tento script na stránku produktu
 * 2. Zavolejte SportLive24Payment.init() po načtení stránky
 * 3. Script automaticky najde tlačítka pro nákup a přidá funkcionalitu
 */

class SportLive24Payment {
    constructor() {
        this.apiBaseUrl = 'https://dev.sportlive24.tv/api';
        this.currentUser = null;
        this.productSlug = null;
        this.productInfo = null;
        this.isInitialized = false;
    }

    /**
     * Inicializace platebního systému
     */
    async init() {
        if (this.isInitialized) return;

        console.log('SportLive24Payment: Initializing...');

        try {
            // Získání informací o produktu z URL
            this.productSlug = this.extractProductSlugFromUrl();

            if (!this.productSlug) {
                console.warn('SportLive24Payment: Could not extract product slug from URL');
                return;
            }

            // Načtení informací o produktu
            await this.loadProductInfo();

            // Detekce přihlášeného uživatele (pokud je dostupný)
            this.detectCurrentUser();

            // Přidání event listenerů na tlačítka
            this.attachPurchaseButtons();

            this.isInitialized = true;
            console.log('SportLive24Payment: Initialized successfully');

        } catch (error) {
            console.error('SportLive24Payment: Initialization failed:', error);
        }
    }

    /**
     * Extrakce product slug z URL
     */
    extractProductSlugFromUrl() {
        const url = window.location.href;
        const pathname = window.location.pathname;

        // Různé možné URL formáty v Uscreen:
        const patterns = [
            /\/programs\/([^?\/]+)/,           // /programs/product-slug
            /\/events\/([^?\/]+)/,            // /events/product-slug
            /\/products\/([^?\/]+)/,          // /products/product-slug
            /\/courses\/([^?\/]+)/,           // /courses/product-slug
            /\/series\/([^?\/]+)/,            // /series/product-slug
            /\/movies\/([^?\/]+)/,            // /movies/product-slug
            /\/shows\/([^?\/]+)/,             // /shows/product-slug
            /\/([^\/]+)$/                     // /product-slug (root level)
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
                console.log(`SportLive24Payment: Extracted slug "${match[1]}" from URL pattern: ${pattern}`);
                return match[1];
            }
        }

        // Fallback: pokusíme se získat slug z posledního segmentu URL
        const segments = pathname.split('/').filter(segment => segment.length > 0);
        if (segments.length > 0) {
            const lastSegment = segments[segments.length - 1];
            // Ověříme, že to vypadá jako slug (obsahuje pomlčky nebo je alfanumerické)
            if (lastSegment.match(/^[a-zA-Z0-9-_]+$/)) {
                console.log(`SportLive24Payment: Using last URL segment as slug: "${lastSegment}"`);
                return lastSegment;
            }
        }

        console.warn('SportLive24Payment: Could not extract product slug from URL:', url);
        return null;
    }

    /**
     * Načtení informací o produktu z API
     */
    async loadProductInfo() {
        try {
            console.log(`SportLive24Payment: Loading product info for: ${this.productSlug}`);
            const response = await fetch(`${this.apiBaseUrl}/product/${this.productSlug}/`);

            if (response.ok) {
                this.productInfo = await response.json();
                console.log('SportLive24Payment: Product info loaded:', this.productInfo);
            } else {
                const errorText = await response.text();
                console.warn('SportLive24Payment: Product not found in payment system:', response.status, errorText);

                // Try mock endpoint as fallback
                try {
                    console.log('SportLive24Payment: Trying mock endpoint...');
                    const mockResponse = await fetch(`${this.apiBaseUrl}/mock-product/${this.productSlug}/`);
                    if (mockResponse.ok) {
                        this.productInfo = await mockResponse.json();
                        console.log('SportLive24Payment: Using mock product info:', this.productInfo);
                        return;
                    }
                } catch (mockError) {
                    console.warn('SportLive24Payment: Mock endpoint also failed:', mockError);
                }

                // Final fallback to hardcoded data
                this.productInfo = {
                    product_id: 3647641,
                    slug: this.productSlug,
                    name: 'J&J Productions',
                    price: 999.00,
                    currency: 'CZK',
                    available: true,
                    source: 'hardcoded-fallback'
                };
                console.log('SportLive24Payment: Using hardcoded fallback product info:', this.productInfo);
            }
        } catch (error) {
            console.warn('SportLive24Payment: Could not load product info:', error);

            // Use fallback data
            this.productInfo = {
                product_id: 3647641,
                slug: this.productSlug,
                name: 'J&J Productions',
                price: 999.00,
                currency: 'CZK',
                available: true,
                source: 'fallback'
            };
            console.log('SportLive24Payment: Using fallback product info due to error:', this.productInfo);
        }
    }

    /**
     * Detekce přihlášeného uživatele (pokud je dostupný v Uscreen)
     */
    detectCurrentUser() {
        // Pokusíme se najít informace o uživateli v Uscreen objektech
        if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
            this.currentUser = {
                email: window.Uscreen.user.email,
                name: window.Uscreen.user.name || window.Uscreen.user.first_name + ' ' + window.Uscreen.user.last_name
            };
            console.log('SportLive24Payment: Current user detected:', this.currentUser.email);
        }

        // Alternativní způsoby detekce uživatele
        // Můžete přidat další logiku podle toho, jak Uscreen ukládá informace o uživateli
    }

    /**
     * Přidání event listenerů na tlačítka pro nákup
     */
    attachPurchaseButtons() {
        // Hledáme různé typy tlačítek pro nákup v Uscreen
        const selectors = [
            // Uscreen specifické selektory
            'button[data-uscreen-purchase]',
            'button[data-action="purchase"]',
            'button[data-action="buy"]',
            '.uscreen-purchase-button',
            '.purchase-button',
            '.buy-button',
            '.checkout-button',
            '.add-to-cart',
            '.subscribe-button',

            // Obecné selektory
            'button:contains("Buy")',
            'button:contains("Purchase")',
            'button:contains("Koupit")',
            'button:contains("Subscribe")',
            'button:contains("Watch Now")',
            'button:contains("Get Access")',
            'a[href*="purchase"]',
            'a[href*="buy"]',
            'a[href*="checkout"]',

            // České texty
            'button:contains("Koupit")',
            'button:contains("Přihlásit se")',
            'button:contains("Získat přístup")',

            // Fallback pro vlastní tlačítka
            '.sl24-gopay-button'
        ];

        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                this.attachPurchaseHandler(button);
            });
        });

        // Pokud nenajdeme žádná tlačítka, vytvoříme vlastní
        if (document.querySelectorAll('.sportlive24-purchase-btn').length === 0) {
            this.createCustomPurchaseButton();
        }
    }

    /**
     * Přidání handleru na tlačítko pro nákup
     */
    attachPurchaseHandler(button) {
        // Označíme tlačítko jako zpracované
        if (button.classList.contains('sportlive24-processed')) return;
        button.classList.add('sportlive24-processed');

        // Přidáme event listener
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            await this.handlePurchaseClick();
        });

        console.log('SportLive24Payment: Purchase handler attached to button');
    }

    /**
     * Vytvoření vlastního tlačítka pro nákup
     */
    createCustomPurchaseButton() {
        if (!this.productInfo) return;

        const button = document.createElement('button');
        button.className = 'sportlive24-purchase-btn';
        button.innerHTML = `Koupit za ${this.productInfo.price} CZK`;
        button.style.cssText = `
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 0;
        `;

        // Najdeme vhodné místo pro vložení tlačítka
        const container = document.querySelector('.product-info') ||
                         document.querySelector('.program-details') ||
                         document.querySelector('main') ||
                         document.body;

        container.appendChild(button);
        this.attachPurchaseHandler(button);
    }

    /**
     * Zpracování kliknutí na tlačítko nákupu
     */
    async handlePurchaseClick() {
        try {
            if (this.currentUser && this.currentUser.email) {
                // Uživatel je přihlášený
                await this.handleLoggedInUserPurchase();
            } else {
                // Uživatel není přihlášený - zobrazíme formulář
                await this.handleAnonymousPurchase();
            }
        } catch (error) {
            console.error('SportLive24Payment: Purchase failed:', error);
            alert('Nastala chyba při zpracování platby. Zkuste to prosím znovu.');
        }
    }

    /**
     * Zpracování nákupu pro přihlášeného uživatele
     */
    async handleLoggedInUserPurchase() {
        // Zobrazíme loading
        this.showLoading('Připravujeme platbu...');

        try {
            // Zkontrolujeme, zda uživatel existuje v našem systému
            const checkResponse = await fetch(`${this.apiBaseUrl}/check-user/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: this.currentUser.email
                })
            });

            const checkData = await checkResponse.json();

            // Zobrazíme informaci uživateli
            const message = checkData.exists
                ? '✅ Máte již SportLive24 účet. Po platbě bude automaticky udělen přístup.'
                : '🆕 Bude vytvořen nový SportLive24 účet s přístupovými údaji zaslanými emailem.';

            if (!confirm(`${message}\n\nPokračovat na platbu za ${this.productInfo.price} CZK?`)) {
                this.hideLoading();
                return;
            }

            // Vytvoříme platbu
            const paymentResponse = await fetch(`${this.apiBaseUrl}/create-payment-from-product/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: this.currentUser.email,
                    product_slug: this.productSlug,
                    user_name: this.currentUser.name,
                    return_url: window.location.href
                })
            });

            const paymentData = await paymentResponse.json();

            if (paymentData.error) {
                throw new Error(paymentData.error);
            }

            // Přesměrujeme na GoPay
            window.location.href = paymentData.payment_url;

        } catch (error) {
            this.hideLoading();
            throw error;
        }
    }

    /**
     * Zpracování nákupu pro nepřihlášeného uživatele
     */
    async handleAnonymousPurchase() {
        const email = prompt('Zadejte váš email pro vytvoření účtu a platbu:');

        if (!email || !this.isValidEmail(email)) {
            alert('Prosím zadejte platný email.');
            return;
        }

        const name = prompt('Zadejte vaše jméno:') || email.split('@')[0];

        // Zobrazíme loading
        this.showLoading('Připravujeme platbu...');

        try {
            // Vytvoříme platbu
            const paymentResponse = await fetch(`${this.apiBaseUrl}/create-payment-from-product/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    product_slug: this.productSlug,
                    user_name: name,
                    return_url: window.location.href
                })
            });

            const paymentData = await paymentResponse.json();

            if (paymentData.error) {
                throw new Error(paymentData.error);
            }

            // Přesměrujeme na GoPay
            window.location.href = paymentData.payment_url;

        } catch (error) {
            this.hideLoading();
            throw error;
        }
    }

    /**
     * Validace emailu
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Zobrazení loading indikátoru
     */
    showLoading(message = 'Načítání...') {
        // Odstraníme existující loading pokud existuje
        this.hideLoading();

        const loading = document.createElement('div');
        loading.id = 'sportlive24-loading';
        loading.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
            font-size: 18px;
        `;
        loading.innerHTML = `<div>${message}</div>`;

        document.body.appendChild(loading);
    }

    /**
     * Skrytí loading indikátoru
     */
    hideLoading() {
        const loading = document.getElementById('sportlive24-loading');
        if (loading) {
            loading.remove();
        }
    }
}

// Vytvoření globální instance
window.SportLive24Payment = new SportLive24Payment();

// Automatická inicializace po načtení stránky
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.SportLive24Payment.init();
    });
} else {
    window.SportLive24Payment.init();
}
