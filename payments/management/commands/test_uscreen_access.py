"""
Test command to verify Uscreen access granting functionality
"""
import os
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from payments.services.uscreen_service import UscreenService
from payments.models import UscreenUser, UscreenProgram
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test Uscreen access granting for specific user and product.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            required=True,
            help='Uscreen user ID to grant access to'
        )
        parser.add_argument(
            '--email',
            type=str,
            required=True,
            help='User email for verification'
        )
        parser.add_argument(
            '--product-id',
            type=int,
            required=True,
            help='Uscreen product ID to grant access to'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually granting access'
        )

    def handle(self, *args, **options):
        """Test Uscreen access granting"""

        user_id = options['user_id']
        email = options['email']
        product_id = options['product_id']
        dry_run = options['dry_run']

        # Check if API key is set
        api_key = os.environ.get('USCREEN_API_KEY')
        if not api_key:
            self.stdout.write(
                self.style.ERROR("❌ USCREEN_API_KEY environment variable is not set!")
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f"✅ USCREEN_API_KEY is set (ends with: ...{api_key[-8:]})")
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING("🧪 DRY RUN MODE - No actual access will be granted")
            )

        self.stdout.write("=" * 60)
        self.stdout.write(f"🧪 Testing Uscreen access granting...")
        self.stdout.write(f"👤 User ID: {user_id}")
        self.stdout.write(f"📧 Email: {email}")
        self.stdout.write(f"🎬 Product ID: {product_id}")
        self.stdout.write("=" * 60)

        try:
            uscreen_service = UscreenService()

            # Step 1: Verify user exists
            self.stdout.write("\n1️⃣ Verifying user exists in Uscreen...")
            try:
                user_data = uscreen_service.get_user_by_email(email)
                if user_data:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ User found: {user_data.get('name')} ({user_data.get('email')})")
                    )
                    self.stdout.write(f"   Uscreen ID: {user_data.get('id')}")
                    self.stdout.write(f"   Subscriber: {user_data.get('subscriber', False)}")

                    # Verify the user ID matches
                    if user_data.get('id') != user_id:
                        self.stdout.write(
                            self.style.WARNING(f"⚠️  User ID mismatch! Expected: {user_id}, Found: {user_data.get('id')}")
                        )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"❌ User not found with email: {email}")
                    )
                    return
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Error checking user: {e}")
                )
                return

            # Step 2: Check product in our database
            self.stdout.write("\n2️⃣ Checking product in local database...")
            try:
                program = UscreenProgram.objects.filter(uscreen_id=product_id).first()
                if program:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Product found in local DB: {program.title}")
                    )
                    self.stdout.write(f"   Slug: {program.slug}")
                    self.stdout.write(f"   Price: {program.price_czk} CZK")
                    self.stdout.write(f"   Content Type: {program.content_type}")
                else:
                    self.stdout.write(
                        self.style.WARNING(f"⚠️  Product not found in local DB with ID: {product_id}")
                    )
                    self.stdout.write("   This is OK - we can still grant access via API")
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"⚠️  Error checking local product: {e}")
                )

            # Step 3: Grant access (or simulate in dry run)
            self.stdout.write("\n3️⃣ Granting access to product...")

            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f"🧪 DRY RUN: Would grant access to product {product_id} for user {user_id}")
                )
                self.stdout.write("   API call would be:")
                self.stdout.write(f"   POST /users/{user_id}/accesses")
                self.stdout.write(f"   Body: {{'product_id': {product_id}, 'access_type': 'purchase'}}")
            else:
                try:
                    access_response = uscreen_service.grant_access_to_product(
                        user_id=user_id,
                        product_id=product_id
                    )

                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Access granted successfully!")
                    )
                    self.stdout.write(f"   Response: {access_response}")

                    # Update local user record if exists
                    try:
                        uscreen_user, created = UscreenUser.objects.get_or_create(
                            uscreen_id=user_id,
                            defaults={
                                'email': email,
                                'name': user_data.get('name', email.split('@')[0]),
                                'subscriber': user_data.get('subscriber', False),
                                'created_at': timezone.now()
                            }
                        )

                        if created:
                            self.stdout.write(
                                self.style.SUCCESS(f"✅ Created local user record")
                            )
                        else:
                            self.stdout.write(
                                self.style.SUCCESS(f"✅ Updated existing local user record")
                            )

                    except Exception as local_error:
                        self.stdout.write(
                            self.style.WARNING(f"⚠️  Could not update local user record: {local_error}")
                        )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Failed to grant access: {e}")
                    )
                    return

            # Step 4: Summary
            self.stdout.write("\n" + "=" * 60)
            self.stdout.write("📋 TEST SUMMARY:")
            self.stdout.write("=" * 60)

            if dry_run:
                self.stdout.write("🧪 DRY RUN completed successfully")
                self.stdout.write("✅ User verification: PASSED")
                self.stdout.write("✅ API connectivity: VERIFIED")
                self.stdout.write("🔄 Access granting: SIMULATED")
            else:
                self.stdout.write("🎉 LIVE TEST completed successfully!")
                self.stdout.write("✅ User verification: PASSED")
                self.stdout.write("✅ Access granting: COMPLETED")
                self.stdout.write(f"👤 User {email} now has access to product {product_id}")

            self.stdout.write("\n🔍 Next steps:")
            self.stdout.write("   1. User can now access the product on sportlive24.tv")
            self.stdout.write("   2. Check access in Uscreen admin panel")
            self.stdout.write("   3. Test the complete payment flow")

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Test failed: {e}")
            )
            raise
